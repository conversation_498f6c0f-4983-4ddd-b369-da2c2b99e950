import { useState, useEffect, useMemo } from 'react';
import { Project, Task, User, ActivityItem } from '../types';
import { getFromStorage, saveToStorage } from '../utils/storage';

export const useData = (user: User | null, isAuthReady: boolean) => {
    const [projects, setProjects] = useState<Project[]>([]);
    const [activeProject, setActiveProject] = useState<Project | null>(null);
    const [tasks, setTasks] = useState<Task[]>([]);
    const [allTasks, setAllTasks] = useState<Task[]>([]);
    const [isLoading, setIsLoading] = useState(true);

    const statuses = useMemo(() => ['Backlog', 'In Progress', 'Review', 'Done'], []);

    // Data Loading from localStorage (User-Specific)
    useEffect(() => {
        if (!isAuthReady || !user) {
            setIsLoading(false);
            return;
        }
        
        setIsLoading(true);
        const projectsKey = `momentum_projects_${user.publicKey}`;
        const tasksKey = `momentum_tasks_${user.publicKey}`;

        const storedProjects = getFromStorage(projectsKey, []);
        const storedTasks = getFromStorage(tasksKey, []);

        setProjects(storedProjects);
        setAllTasks(storedTasks);

        if (!activeProject && storedProjects.length > 0) {
            setActiveProject(storedProjects[0]);
        } else if (storedProjects.length === 0) {
            setActiveProject(null);
        }
        setIsLoading(false);
    }, [isAuthReady, user, activeProject]);

    // Filter tasks when active project changes
    useEffect(() => {
        if (activeProject) {
            const projectTasks = allTasks.filter(t => t.projectId === activeProject.id);
            setTasks(projectTasks);
        } else {
            setTasks([]);
        }
    }, [activeProject, allTasks]);

    // Memoized Data
    const allMilestones = useMemo(() => 
        allTasks.filter(t => t.isMilestone).map(t => ({
            ...t, 
            projectName: projects.find(p => p.id === t.projectId)?.name 
        })), 
        [allTasks, projects]
    );

    const tasksByStatus = useMemo(() => {
        const grouped: Record<string, Task[]> = {};
        statuses.forEach(status => {
            grouped[status] = tasks.filter(task => task.status === status).sort((a, b) => a.order - b.order);
        });
        return grouped;
    }, [tasks, statuses]);

    const tasksWithDates = useMemo(() => 
        tasks.filter(task => !!task.startDate && !!task.dueDate), 
        [tasks]
    );

    const handleSaveProject = (projectName: string) => {
        if (!user) return;
        
        const newProject: Project = { 
            id: crypto.randomUUID(), 
            name: projectName, 
            createdAt: new Date().toISOString() 
        };
        const updatedProjects = [...projects, newProject];
        setProjects(updatedProjects);
        saveToStorage(`momentum_projects_${user.publicKey}`, updatedProjects);
        setActiveProject(newProject);
    };

    const handleSaveTask = (taskData: Partial<Task>) => {
        if (!user) return;
        
        const projectId = taskData.projectId || activeProject?.id;
        if (!projectId) { 
            alert("Could not find a project for this task."); 
            return; 
        }

        let updatedTasks: Task[];
        const timestamp = new Date().toISOString();

        if (taskData.id) { // Update
            const originalTask = allTasks.find(t => t.id === taskData.id);
            if (!originalTask) return;

            const updatedTask = { ...originalTask, ...taskData } as Task;
            const historyEntry: ActivityItem = { 
                id: crypto.randomUUID(), 
                type: 'history', 
                changedBy: user.publicKey, 
                timestamp, 
                changes: [] 
            };
            
            const dataToCompare = { ...taskData };
            delete (dataToCompare as Record<string, unknown>).id;
            delete (dataToCompare as Record<string, unknown>).projectId;

            Object.keys(dataToCompare).forEach(key => {
                if(JSON.stringify((originalTask as unknown as Record<string, unknown>)[key]) !== JSON.stringify((dataToCompare as Record<string, unknown>)[key])){
                     historyEntry.changes!.push({ 
                         field: key, 
                         oldValue: String((originalTask as unknown as Record<string, unknown>)[key] || 'Not set'), 
                         newValue: String((dataToCompare as Record<string, unknown>)[key] || 'Not set') 
                     });
                }
            });
            
            if(historyEntry.changes!.length > 0) {
                updatedTask.activity = [...(updatedTask.activity || []), historyEntry];
            }
            updatedTasks = allTasks.map(t => t.id === taskData.id ? updatedTask : t);
        } else { // Create
            const newTask: Task = {
                ...taskData as Task,
                id: crypto.randomUUID(),
                projectId,
                order: tasks.filter(t => t.status === taskData.status).length,
                createdAt: timestamp,
                activity: [{ 
                    id: crypto.randomUUID(), 
                    type: 'history', 
                    changes: [{ field: 'Task', oldValue: '', newValue: 'Created' }], 
                    changedBy: user.publicKey, 
                    timestamp 
                }]
            };
            updatedTasks = [...allTasks, newTask];
        }
        
        setAllTasks(updatedTasks);
        saveToStorage(`momentum_tasks_${user.publicKey}`, updatedTasks);
    };

    const handleAddComment = (taskId: string, text: string) => {
        if (!text.trim() || !user) return;
        
        const updatedTasks = allTasks.map(task => {
            if (task.id === taskId) {
                const newComment: ActivityItem = { 
                    id: crypto.randomUUID(), 
                    type: 'comment', 
                    text, 
                    authorId: user.publicKey, 
                    authorName: user.name, 
                    timestamp: new Date().toISOString() 
                };
                return { ...task, activity: [...(task.activity || []), newComment] };
            }
            return task;
        });
        
        setAllTasks(updatedTasks);
        saveToStorage(`momentum_tasks_${user.publicKey}`, updatedTasks);
    };

    const handleDeleteTask = (taskId: string) => {
        if (!user) return;
        
        if (window.confirm("Are you sure?")) {
            const updatedTasks = allTasks.filter(t => t.id !== taskId);
            setAllTasks(updatedTasks);
            saveToStorage(`momentum_tasks_${user.publicKey}`, updatedTasks);
        }
    };

    const clearUserData = () => {
        setProjects([]);
        setAllTasks([]);
        setActiveProject(null);
    };

    return {
        projects,
        setProjects,
        activeProject,
        setActiveProject,
        tasks,
        setTasks,
        allTasks,
        setAllTasks,
        isLoading,
        statuses,
        allMilestones,
        tasksByStatus,
        tasksWithDates,
        handleSaveProject,
        handleSaveTask,
        handleAddComment,
        handleDeleteTask,
        clearUserData
    };
};
