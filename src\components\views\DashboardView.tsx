import React from 'react';
import { DashboardViewProps } from '../../types';
import { GanttView } from './GanttView';

export const DashboardView: React.FC<DashboardViewProps> = ({ project, tasks, allMilestones }) => (
    <div className="text-white">
        <h1 className="text-3xl font-bold mb-6">Master Dashboard</h1>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-gray-800 p-6 rounded-lg">
                <h3 className="text-gray-400 text-sm font-medium">Active Project</h3>
                <p className="text-3xl font-semibold mt-2 truncate">{project?.name || 'N/A'}</p>
            </div>
            <div className="bg-gray-800 p-6 rounded-lg">
                <h3 className="text-gray-400 text-sm font-medium">Tasks in Project</h3>
                <p className="text-3xl font-semibold mt-2">{tasks.length}</p>
            </div>
            <div className="bg-gray-800 p-6 rounded-lg">
                <h3 className="text-gray-400 text-sm font-medium">Total Milestones</h3>
                <p className="text-3xl font-semibold mt-2">{allMilestones.length}</p>
            </div>
            <div className="bg-gray-800 p-6 rounded-lg">
                <h3 className="text-gray-400 text-sm font-medium">Completed in Project</h3>
                <p className="text-3xl font-semibold mt-2">{tasks.filter(t => t.status === 'Done').length}</p>
            </div>
        </div>
        <div className="mt-8">
            <h2 className="text-xl font-bold mb-4">All Project Milestones</h2>
            <GanttView tasks={allMilestones} />
        </div>
    </div>
);
