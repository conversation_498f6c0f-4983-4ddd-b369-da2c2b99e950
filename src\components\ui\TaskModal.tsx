import React, { useState } from 'react';
import { Star, Trash2 } from 'lucide-react';
import { Task, Project } from '../../types';
import { ActivityItemComponent } from './ActivityItem';

interface TaskModalProps {
    task: Task | null;
    project: Project | null | undefined;
    onClose: () => void;
    onSave: (taskData: Partial<Task>) => void;
    onDelete: (taskId: string) => void;
    onAddComment: (taskId: string, text: string) => void;
    statuses: string[];
}

export const TaskModal: React.FC<TaskModalProps> = ({ 
    task, 
    project, 
    onClose, 
    onSave, 
    onDelete, 
    onAddComment, 
    statuses 
}) => {
    const [title, setTitle] = useState(task?.title || '');
    const [description, setDescription] = useState(task?.description || '');
    const [status, setStatus] = useState(task?.status || statuses[0]);
    const [priority, setPriority] = useState(task?.priority || 'None');
    const [startDate, setStartDate] = useState(task?.startDate ? new Date(task.startDate).toISOString().split('T')[0] : '');
    const [dueDate, setDueDate] = useState(task?.dueDate ? new Date(task.dueDate).toISOString().split('T')[0] : '');
    const [deadline, setDeadline] = useState(task?.deadline ? new Date(task.deadline).toISOString().split('T')[0] : '');
    const [isMilestone, setIsMilestone] = useState(task?.isMilestone || false);
    const [newComment, setNewComment] = useState("");

    const handleSave = (e: React.FormEvent) => {
        e.preventDefault();
        if (!title.trim()) return;
        
        const taskData: Partial<Task> = {
            id: task?.id,
            projectId: project?.id,
            title: title.trim(),
            description: description.trim(),
            status,
            priority,
            startDate: startDate ? new Date(startDate).toISOString() : undefined,
            dueDate: dueDate ? new Date(dueDate).toISOString() : undefined,
            deadline: deadline ? new Date(deadline).toISOString() : undefined,
            isMilestone
        };
        onSave(taskData);
    };
    
    const handleAddComment = (e: React.FormEvent) => {
        e.preventDefault();
        if (task?.id) {
            onAddComment(task.id, newComment);
            setNewComment("");
        }
    };

    return (
        <div className="fixed inset-0 bg-black/70 z-50 flex items-center justify-center p-4 backdrop-blur-sm" onClick={onClose}>
            <div className="bg-gray-800 rounded-xl shadow-2xl w-full max-w-4xl border border-gray-700 grid grid-cols-3" onClick={e => e.stopPropagation()}>
                <div className="col-span-2 border-r border-gray-700">
                    <form onSubmit={handleSave} className="flex flex-col h-full">
                        <div className="p-6">
                            <div className="flex justify-between items-start">
                                <input
                                    type="text"
                                    value={title}
                                    onChange={(e) => setTitle(e.target.value)}
                                    placeholder="Task title..."
                                    className="w-full bg-transparent text-2xl font-bold text-white focus:outline-none"
                                    autoFocus
                                />
                                <button
                                    type="button"
                                    onClick={() => setIsMilestone(!isMilestone)}
                                    title="Set as milestone"
                                    className={`p-2 rounded-full ${isMilestone ? 'bg-amber-500/20 text-amber-400' : 'text-gray-500 hover:bg-gray-700'}`}
                                >
                                    <Star size={20}/>
                                </button>
                            </div>
                            <textarea
                                value={description}
                                onChange={(e) => setDescription(e.target.value)}
                                placeholder="Add a description..."
                                className="w-full bg-transparent text-gray-300 mt-4 h-20 resize-none focus:outline-none"
                            />
                        </div>
                        <div className="p-6 border-t border-gray-700 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <div>
                                <label className="text-xs text-gray-400 font-semibold mb-1 block">Status</label>
                                <select value={status} onChange={e => setStatus(e.target.value)} className="w-full bg-gray-700/50 p-2 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                    {statuses.map(s => <option key={s} value={s}>{s}</option>)}
                                </select>
                            </div>
                            <div>
                                <label className="text-xs text-gray-400 font-semibold mb-1 block">Priority</label>
                                <select value={priority} onChange={e => setPriority(e.target.value)} className="w-full bg-gray-700/50 p-2 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                    <option value="None">None</option>
                                    <option value="Low">Low</option>
                                    <option value="Medium">Medium</option>
                                    <option value="High">High</option>
                                </select>
                            </div>
                            <div className="lg:col-span-3 grid grid-cols-3 gap-4">
                                <div>
                                    <label className="text-xs text-gray-400 font-semibold mb-1 block">Start Date</label>
                                    <input type="date" value={startDate} onChange={e => setStartDate(e.target.value)} className="w-full bg-gray-700/50 p-2 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-indigo-500" />
                                </div>
                                <div>
                                    <label className="text-xs text-gray-400 font-semibold mb-1 block">Due Date</label>
                                    <input type="date" value={dueDate} onChange={e => setDueDate(e.target.value)} className="w-full bg-gray-700/50 p-2 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-indigo-500" />
                                </div>
                                <div>
                                    <label className="text-xs text-gray-400 font-semibold mb-1 block">Deadline</label>
                                    <input type="date" value={deadline} onChange={e => setDeadline(e.target.value)} className="w-full bg-gray-700/50 p-2 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-red-500" />
                                </div>
                            </div>
                        </div>
                        <div className="flex-grow"></div>
                        <div className="flex justify-between items-center p-6 bg-gray-800/50 border-t border-gray-700 rounded-b-xl">
                            {task?.id &&
                                <button type="button" onClick={() => onDelete(task.id)} className="text-red-400 hover:text-red-300 transition-colors text-sm font-medium flex items-center gap-2">
                                    <Trash2 size={16} /> Delete Task
                                </button>
                            }
                            <div className="flex-grow"></div>
                            <div className="flex items-center gap-4">
                                <button type="button" onClick={onClose} className="px-4 py-2 rounded-md text-gray-300 hover:bg-gray-700 transition-colors">Cancel</button>
                                <button type="submit" className="px-4 py-2 rounded-md bg-indigo-600 text-white hover:bg-indigo-500 transition-colors">
                                    {task?.id ? 'Save Changes' : 'Create Task'}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
                <div className="col-span-1 flex flex-col">
                    <h3 className="p-4 font-semibold text-white border-b border-gray-700">Activity</h3>
                    <div className="flex-grow p-4 space-y-4 overflow-y-auto">
                        {task?.activity?.sort((a,b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()).map(item => 
                            <ActivityItemComponent key={item.id} item={item} />
                        )}
                    </div>
                    <div className="p-4 border-t border-gray-700">
                        <form onSubmit={handleAddComment} className="flex gap-2">
                            <input
                                type="text"
                                value={newComment}
                                onChange={e => setNewComment(e.target.value)}
                                placeholder="Add a comment..."
                                className="w-full bg-gray-700/50 p-2 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-indigo-500"
                            />
                            <button type="submit" className="px-4 py-2 rounded-md bg-indigo-600 text-white hover:bg-indigo-500 disabled:bg-gray-600" disabled={!newComment.trim()}>
                                Send
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    );
};
