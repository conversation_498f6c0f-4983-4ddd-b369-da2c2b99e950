import React, { useState, useMemo } from 'react';
import { ChevronLeft, ChevronRight, Clock } from 'lucide-react';
import { CalendarViewProps, Task } from '../../types';

export const CalendarView: React.FC<CalendarViewProps> = ({ tasks, onEditTask, onNewTaskForDate }) => {
    const [currentDate, setCurrentDate] = useState(new Date());
    const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const lastDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
    
    const tasksByDate = useMemo(() => {
        const grouped: Record<string, Task[]> = {};
        (tasks || []).forEach(task => {
            if(!task.dueDate) return;
            const date = new Date(task.dueDate).toISOString().split('T')[0];
            if (!grouped[date]) grouped[date] = [];
            grouped[date].push(task);
        });
        return grouped;
    }, [tasks]);
    
    const changeMonth = (offset: number) => setCurrentDate(prev => new Date(prev.getFullYear(), prev.getMonth() + offset, 1));
    
    const calendarDays: React.ReactElement[] = Array.from({ length: firstDayOfMonth.getDay() }, (_, i) => 
        <div key={`empty-${i}`} className="border-r border-b border-gray-700/50"></div>
    );

    for (let day = 1; day <= lastDayOfMonth.getDate(); day++) {
        const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
        const dateString = date.toISOString().split('T')[0];
        const isToday = new Date().toISOString().split('T')[0] === dateString;
        const dayTasks = tasksByDate[dateString] || [];
        
        calendarDays.push(
            <div 
                key={day} 
                className="border-r border-b border-gray-700/50 p-2 min-h-[140px] flex flex-col hover:bg-gray-700/30 cursor-pointer" 
                onClick={() => onNewTaskForDate(date)}
            >
                <span className={`font-semibold ${isToday ? 'bg-indigo-600 rounded-full w-8 h-8 flex items-center justify-center text-white' : 'text-gray-300'}`}>
                    {day}
                </span>
                <div className="mt-2 space-y-1 overflow-y-auto">
                    {dayTasks.map(task => (
                        <div 
                            key={task.id} 
                            onClick={(e) => { e.stopPropagation(); onEditTask(task); }} 
                            className={`p-1.5 rounded-md text-sm text-gray-200 cursor-pointer truncate flex flex-col ${task.isMilestone ? 'bg-amber-600' : 'bg-gray-700 hover:bg-indigo-500'}`}
                        >
                            <span>{task.title}</span>
                            {task.deadline && !task.isMilestone && (
                                <span className="text-xs text-red-400/80 flex items-center gap-1 mt-1">
                                    <Clock size={12}/>Must be done
                                </span>
                            )}
                        </div>
                    ))}
                </div>
            </div>
        );
    }
    
    const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    
    return (
        <div className="bg-gray-800/50 rounded-lg border border-gray-700/50">
            <header className="flex items-center justify-between p-4 border-b border-gray-700/50">
                <div className="flex items-center gap-4">
                    <button onClick={() => changeMonth(-1)} className="p-2 rounded-md hover:bg-gray-700">
                        <ChevronLeft/>
                    </button>
                    <h2 className="text-xl font-semibold text-white">
                        {currentDate.toLocaleString('default', { month: 'long', year: 'numeric' })}
                    </h2>
                    <button onClick={() => changeMonth(1)} className="p-2 rounded-md hover:bg-gray-700">
                        <ChevronRight/>
                    </button>
                </div>
                <button 
                    onClick={() => setCurrentDate(new Date())} 
                    className="px-4 py-2 rounded-md border border-gray-600 hover:bg-gray-700 text-sm"
                >
                    Today
                </button>
            </header>
            <div className="grid grid-cols-7">
                {weekDays.map(day => (
                    <div key={day} className="text-center p-3 font-semibold text-gray-400 border-b border-r border-gray-700/50">
                        {day}
                    </div>
                ))}
                {calendarDays}
            </div>
        </div>
    );
};
