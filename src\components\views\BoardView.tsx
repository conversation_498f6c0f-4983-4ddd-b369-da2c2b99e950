import React from 'react';
import { DndContext, closestCenter, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import type { DragEndEvent } from '@dnd-kit/core';
import { arrayMove } from '@dnd-kit/sortable';
import { BoardViewProps } from '../../types';
import { getFromStorage, saveToStorage } from '../../utils/storage';
import { Column } from '../ui/Column';

export const BoardView: React.FC<BoardViewProps> = ({ 
    tasks, 
    setTasks, 
    statuses, 
    tasksByStatus, 
    onEditTask, 
    onAddTask, 
    activeProject, 
    user 
}) => {
    const sensors = useSensors(useSensor(PointerSensor, { activationConstraint: { distance: 8 } }));
    
    const onDragEnd = async (event: DragEndEvent) => {
        const { active, over } = event;
        if (!over || active.id === over.id || !activeProject) return;
        
        const oldIndex = tasks.findIndex(t => t.id === String(active.id));
        const newIndex = tasks.findIndex(t => t.id === String(over.id));
        
        if(oldIndex !== -1 && newIndex !== -1) {
            const newTasks = arrayMove(tasks, oldIndex, newIndex);
            setTasks(newTasks);
            const allStoredTasks = getFromStorage(`momentum_tasks_${user.publicKey}`);
            const updatedTasks = allStoredTasks.map((t: unknown) => newTasks.find(nt => nt.id === (t as { id: string }).id) || t);
            saveToStorage(`momentum_tasks_${user.publicKey}`, updatedTasks);
        }
    };
    
    return (
        <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={onDragEnd}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {statuses.map(status => (
                    <Column 
                        key={status} 
                        id={status} 
                        title={status} 
                        tasks={tasksByStatus[status] || []} 
                        onEditTask={onEditTask} 
                        onAddTask={onAddTask}
                    />
                ))}
            </div>
        </DndContext>
    );
};
