import { useState, useEffect } from 'react';
import { User } from '../types';

export const useAuth = () => {
    const [user, setUser] = useState<User | null>(null);
    const [isAuthReady, setIsAuthReady] = useState(false);
    const [authError, setAuthError] = useState<string | null>(null);

    useEffect(() => {
        const session = sessionStorage.getItem('momentum_session');
        if (!session) {
            setIsAuthReady(true); // Stop loading, but user will be null
        } else {
            try {
                const parsedSession = JSON.parse(session);

                // Validate session data structure
                if (!parsedSession.name || !parsedSession.publicKey) {
                    console.warn('Invalid session data structure');
                    setAuthError('Invalid session data. Please login again.');
                    sessionStorage.removeItem('momentum_session');
                    setIsAuthReady(true);
                    return;
                }

                // Validate public key format (basic check)
                if (typeof parsedSession.publicKey !== 'string' || parsedSession.publicKey.length < 10) {
                    console.warn('Invalid public key format');
                    setAuthError('Invalid authentication data. Please login again.');
                    sessionStorage.removeItem('momentum_session');
                    setIsAuthReady(true);
                    return;
                }

                // Check session timestamp for security (24 hour timeout)
                const sessionTimestamp = parsedSession.timestamp || 0;
                const currentTime = Date.now();
                const sessionAge = currentTime - sessionTimestamp;
                const maxSessionAge = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

                if (sessionAge > maxSessionAge) {
                    console.warn('Session expired');
                    setAuthError('Your session has expired. Please login again.');
                    sessionStorage.removeItem('momentum_session');
                    setIsAuthReady(true);
                    return;
                }

                setUser({
                    name: parsedSession.name,
                    publicKey: parsedSession.publicKey,
                    uid: parsedSession.publicKey // Use public key as the unique ID
                });
                setIsAuthReady(true);
            } catch (error) {
                console.error('Error parsing session data:', error);
                setAuthError('Corrupted session data. Please login again.');
                sessionStorage.removeItem('momentum_session');
                setIsAuthReady(true);
            }
        }
    }, []);

    // Session refresh on activity
    useEffect(() => {
        if (!user) return;

        const refreshSession = () => {
            const session = sessionStorage.getItem('momentum_session');
            if (session) {
                try {
                    const parsedSession = JSON.parse(session);
                    const updatedSession = {
                        ...parsedSession,
                        timestamp: Date.now() // Refresh timestamp
                    };
                    sessionStorage.setItem('momentum_session', JSON.stringify(updatedSession));
                } catch (error) {
                    console.error('Error refreshing session:', error);
                }
            }
        };

        // Refresh session every 30 minutes of activity
        const refreshInterval = setInterval(refreshSession, 30 * 60 * 1000);

        // Also refresh on user interactions
        const handleUserActivity = () => {
            refreshSession();
        };

        window.addEventListener('click', handleUserActivity);
        window.addEventListener('keydown', handleUserActivity);

        return () => {
            clearInterval(refreshInterval);
            window.removeEventListener('click', handleUserActivity);
            window.removeEventListener('keydown', handleUserActivity);
        };
    }, [user]);

    const handleLogout = () => {
        if (window.confirm('Are you sure you want to logout? You will need to re-authenticate to access your data.')) {
            sessionStorage.removeItem('momentum_session');
            setUser(null);
            return true;
        }
        return false;
    };

    return {
        user,
        setUser,
        isAuthReady,
        authError,
        handleLogout
    };
};
