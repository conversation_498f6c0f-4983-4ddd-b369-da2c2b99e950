import React from 'react';
import { Folder, Plus } from 'lucide-react';
import { NoProjectsViewProps } from '../../types';

export const NoProjectsView: React.FC<NoProjectsViewProps> = ({ onNewProject }) => (
    <div className="flex flex-col items-center justify-center h-full text-center">
        <Folder size={64} className="text-gray-600 mb-4" />
        <h2 className="text-2xl font-bold text-white mb-2">No Projects Yet</h2>
        <p className="text-gray-400 mb-6">Get started by creating your first project.</p>
        <button onClick={onNewProject} className="flex items-center space-x-2 px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-500 transition-colors">
            <Plus size={20} />
            <span>Create Project</span>
        </button>
    </div>
);
