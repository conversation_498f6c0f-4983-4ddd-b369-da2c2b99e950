import React, { useState, useEffect } from 'react';
import { LoginComponentProps, StoredIdentity, User } from '../../types';
import { encryptData, decryptData } from '../../utils/crypto';

export const LoginComponent: React.FC<LoginComponentProps> = ({ authError, onLoginSuccess }) => {
    const [currentView, setCurrentView] = useState<'main' | 'login' | 'create'>('main');
    const [identities, setIdentities] = useState<StoredIdentity[]>([]);
    const [selectedIdentity, setSelectedIdentity] = useState<StoredIdentity | null>(null);
    const [isLoading, setIsLoading] = useState(false);

    // Form states
    const [loginPassword, setLoginPassword] = useState('');
    const [createName, setCreateName] = useState('');
    const [createPassword, setCreatePassword] = useState('');
    const [entropyCollected, setEntropyCollected] = useState(0);
    const [entropy] = useState(() => new Uint8Array(32));

    const requiredEntropy = 1024;
    const canvasRef = React.useRef<HTMLCanvasElement>(null);

    // Load identities on mount
    useEffect(() => {
        loadIdentities();
    }, []);

    const loadIdentities = () => {
        try {
            const storedIdentities = JSON.parse(localStorage.getItem('momentum_identities') || '[]');
            setIdentities(storedIdentities);
        } catch (error) {
            console.error('Error loading identities:', error);
            setIdentities([]);
        }
    };

    const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
        if (entropyCollected >= requiredEntropy) return;

        const canvas = canvasRef.current;
        if (!canvas) return;

        const rect = canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        const time = performance.now();

        entropy[entropyCollected % 32] ^= Math.floor(x * time) & 0xff;
        entropy[(entropyCollected + 1) % 32] ^= Math.floor(y * time) & 0xff;
        setEntropyCollected(prev => prev + 1);

        // Visualization
        const ctx = canvas.getContext('2d');
        if (ctx) {
            ctx.fillStyle = `rgba(${entropy[0]}, ${entropy[10]}, ${entropy[20]}, 0.1)`;
            ctx.beginPath();
            ctx.arc(x % canvas.width, y % canvas.height, entropyCollected % 20, 0, Math.PI * 2);
            ctx.fill();
        }
    };

    const handleCreateIdentity = async (e: React.FormEvent) => {
        e.preventDefault();
        if (entropyCollected < requiredEntropy) {
            alert('Please collect more entropy by moving your mouse over the canvas.');
            return;
        }

        setIsLoading(true);

        try {
            // Generate keypair using collected entropy
            const keyPair = crypto.getRandomValues(new Uint8Array(32));
            for (let i = 0; i < 32; i++) {
                keyPair[i] ^= entropy[i];
            }

            const publicKey = btoa(String.fromCharCode(...keyPair.slice(0, 16)));
            const secretKey = btoa(String.fromCharCode(...keyPair));

            const encryptedSecretKey = await encryptData(secretKey, createPassword);

            const newIdentity = {
                name: createName,
                publicKey,
                encryptedSecretKey
            };

            const updatedIdentities = [...identities, newIdentity];
            localStorage.setItem('momentum_identities', JSON.stringify(updatedIdentities));
            setIdentities(updatedIdentities);

            // Create backup download
            const backupData = { name: createName, publicKey, encryptedSecretKey };
            const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(backupData, null, 2));
            const downloadAnchorNode = document.createElement('a');
            downloadAnchorNode.setAttribute("href", dataStr);
            downloadAnchorNode.setAttribute("download", `${createName}_momentum_identity.json`);
            document.body.appendChild(downloadAnchorNode);
            downloadAnchorNode.click();
            downloadAnchorNode.remove();

            alert(`Identity for "${createName}" created! Please log in.`);
            setCurrentView('main');
            setCreateName('');
            setCreatePassword('');
            setEntropyCollected(0);
        } catch (error) {
            console.error('Error creating identity:', error);
            alert('Failed to create identity. Please try again.');
        } finally {
            setIsLoading(false);
        }
    };

    const handleLogin = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!selectedIdentity || !loginPassword) return;

        setIsLoading(true);

        try {
            const decryptedSecretKey = await decryptData(selectedIdentity.encryptedSecretKey, loginPassword);

            if (decryptedSecretKey) {
                const sessionIdentity = {
                    name: selectedIdentity.name,
                    publicKey: selectedIdentity.publicKey,
                    secretKey: decryptedSecretKey,
                    timestamp: Date.now()
                };
                sessionStorage.setItem('momentum_session', JSON.stringify(sessionIdentity));

                // Call the success callback
                onLoginSuccess({
                    name: selectedIdentity.name,
                    publicKey: selectedIdentity.publicKey,
                    uid: selectedIdentity.publicKey
                });
            } else {
                alert('Incorrect password. Please try again.');
            }
        } catch (error) {
            console.error('Login error:', error);
            alert('Login failed. Please try again.');
        } finally {
            setIsLoading(false);
            setLoginPassword('');
        }
    };

    const entropyProgress = Math.min(100, (entropyCollected / requiredEntropy) * 100);

    return (
        <div className="w-screen h-screen flex flex-col items-center justify-center bg-gray-900 text-white">
            <div className="w-full max-w-2xl mx-auto p-8">
                <div className="text-center mb-10">
                    <h1 className="text-4xl font-bold text-white">Momentum</h1>
                    <p className="text-indigo-400">Decentralized Identity Management</p>
                </div>

                {authError && (
                    <div className="mb-6 p-4 bg-red-900/50 border border-red-700 rounded-lg">
                        <p className="text-red-300 text-sm">{authError}</p>
                    </div>
                )}

                {/* Main View */}
                {currentView === 'main' && (
                    <div className="space-y-6">
                        {identities.length > 0 && (
                            <div className="bg-gray-800 p-6 rounded-lg">
                                <h2 className="text-2xl font-semibold mb-4 text-center">Choose Existing Identity</h2>
                                <div className="space-y-3">
                                    {identities.map((identity, index) => (
                                        <button
                                            key={index}
                                            onClick={() => {
                                                setSelectedIdentity(identity);
                                                setCurrentView('login');
                                            }}
                                            className="w-full text-left bg-gray-700 hover:bg-gray-600 text-white font-bold py-3 px-4 rounded-lg transition-colors"
                                        >
                                            <span>{identity.name}</span><br />
                                            <span className="text-xs text-gray-400 font-mono">{identity.publicKey.substring(0, 24)}...</span>
                                        </button>
                                    ))}
                                </div>
                            </div>
                        )}
                        <div className="bg-gray-800 p-6 rounded-lg text-center">
                            <h2 className="text-2xl font-semibold mb-4">Create New Identity</h2>
                            <p className="text-gray-400 mb-4">Generate a new secure identity to use with Momentum.</p>
                            <button
                                onClick={() => setCurrentView('create')}
                                className="w-full bg-indigo-600 hover:bg-indigo-500 text-white font-bold py-3 px-4 rounded-lg transition-colors"
                            >
                                Create New Identity
                            </button>
                        </div>
                    </div>
                )}

                {/* Login View */}
                {currentView === 'login' && selectedIdentity && (
                    <div className="bg-gray-800 p-8 rounded-lg">
                        <h2 className="text-2xl font-semibold mb-4">
                            Unlock Identity: <span className="text-indigo-400">{selectedIdentity.name}</span>
                        </h2>
                        <form onSubmit={handleLogin}>
                            <div className="mb-4">
                                <label htmlFor="login-password" className="block text-gray-300 text-sm font-bold mb-2">
                                    Password
                                </label>
                                <input
                                    type="password"
                                    id="login-password"
                                    value={loginPassword}
                                    onChange={(e) => setLoginPassword(e.target.value)}
                                    className="w-full bg-gray-700 text-white rounded-lg py-3 px-4 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                    required
                                    disabled={isLoading}
                                />
                            </div>
                            <div className="flex items-center justify-between mt-6">
                                <button
                                    type="button"
                                    onClick={() => {
                                        setCurrentView('main');
                                        setSelectedIdentity(null);
                                        setLoginPassword('');
                                    }}
                                    className="text-gray-400 hover:text-white"
                                    disabled={isLoading}
                                >
                                    Back
                                </button>
                                <button
                                    type="submit"
                                    className="bg-indigo-600 hover:bg-indigo-500 text-white font-bold py-3 px-4 rounded-lg transition-colors disabled:bg-gray-600"
                                    disabled={isLoading}
                                >
                                    {isLoading ? 'Unlocking...' : 'Unlock →'}
                                </button>
                            </div>
                        </form>
                    </div>
                )}

                {/* Create View */}
                {currentView === 'create' && (
                    <div className="bg-gray-800 p-8 rounded-lg">
                        <h2 className="text-2xl font-semibold mb-4">Create New Identity</h2>
                        <form onSubmit={handleCreateIdentity}>
                            <div className="mb-4">
                                <label htmlFor="create-name" className="block text-gray-300 text-sm font-bold mb-2">
                                    Name
                                </label>
                                <input
                                    type="text"
                                    id="create-name"
                                    value={createName}
                                    onChange={(e) => setCreateName(e.target.value)}
                                    className="w-full bg-gray-700 text-white rounded-lg py-3 px-4 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                    required
                                    disabled={isLoading}
                                />
                            </div>
                            <div className="mb-6">
                                <label htmlFor="create-password" className="block text-gray-300 text-sm font-bold mb-2">
                                    Password
                                </label>
                                <input
                                    type="password"
                                    id="create-password"
                                    value={createPassword}
                                    onChange={(e) => setCreatePassword(e.target.value)}
                                    className="w-full bg-gray-700 text-white rounded-lg py-3 px-4 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                    required
                                    disabled={isLoading}
                                />
                            </div>

                            <div className="mb-4 p-4 border border-gray-700 rounded-lg">
                                <p className="text-sm text-gray-300 mb-2">
                                    Move your mouse over the area below to generate randomness for your security keys.
                                </p>
                                <canvas
                                    ref={canvasRef}
                                    onMouseMove={handleMouseMove}
                                    className="w-full h-40 rounded-md bg-gray-700 cursor-crosshair"
                                    width={400}
                                    height={160}
                                />
                                <div className="w-full bg-gray-700 rounded-full h-2.5 mt-2">
                                    <div
                                        className="bg-green-500 h-2.5 rounded-full transition-all duration-300"
                                        style={{ width: `${entropyProgress}%` }}
                                    />
                                </div>
                                <p className="text-xs text-gray-400 mt-1">
                                    Entropy: {entropyCollected}/{requiredEntropy} ({entropyProgress.toFixed(1)}%)
                                </p>
                            </div>

                            <div className="flex items-center justify-between mt-6">
                                <button
                                    type="button"
                                    onClick={() => {
                                        setCurrentView('main');
                                        setCreateName('');
                                        setCreatePassword('');
                                        setEntropyCollected(0);
                                    }}
                                    className="text-gray-400 hover:text-white"
                                    disabled={isLoading}
                                >
                                    Back
                                </button>
                                <button
                                    type="submit"
                                    className="bg-indigo-600 hover:bg-indigo-500 text-white font-bold py-3 px-4 rounded-lg transition-colors disabled:bg-gray-600"
                                    disabled={isLoading || entropyProgress < 100}
                                >
                                    {isLoading ? 'Generating...' : 'Generate & Save Identity'}
                                </button>
                            </div>
                        </form>
                    </div>
                )}

                <div className="text-center mt-6 text-sm text-gray-400">
                    <p>🔒 Your identity is stored securely on your device</p>
                    <p>🌐 No data is sent to external servers</p>
                </div>
            </div>
        </div>
    );
};
